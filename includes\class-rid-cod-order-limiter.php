<?php
/**
 * RID COD Order Limiter
 * 
 * Handles the "Prevent 2nd order in 24hours" functionality
 * Prevents customers from placing multiple orders within 24 hours based on phone number and IP address
 * 
 * @package RID_COD
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class RID_COD_Order_Limiter {

    /**
     * Constructor
     */
    public function __construct() {
        // Hook into the order processing before order creation
        add_action('rid_cod_before_order_creation', array($this, 'check_order_limit'), 10, 1);
        
        // Hook into successful order creation to record the order
        add_action('rid_cod_after_order_creation', array($this, 'record_order'), 10, 2);
    }

    /**
     * Check if customer can place an order based on 24-hour limit
     * 
     * @param array $order_data The order data being processed
     * @throws Exception if order should be blocked
     */
    public function check_order_limit($order_data) {
        // Check if the feature is enabled
        $prevent_24h_orders = get_option('rid_cod_prevent_2nd_order_24h', 'no') === 'yes';
        
        if (!$prevent_24h_orders) {
            return; // Feature disabled, allow order
        }

        $phone = isset($order_data['phone']) ? sanitize_text_field($order_data['phone']) : '';
        $customer_ip = $this->get_customer_ip();

        // Clean phone number (remove spaces and normalize format)
        $phone = preg_replace('/\s+/', '', $phone);
        
        if (empty($phone)) {
            return; // No phone number, can't check
        }

        // Check for recent orders by phone number
        $recent_order_by_phone = $this->has_recent_order_by_phone($phone);
        
        // Check for recent orders by IP address
        $recent_order_by_ip = $this->has_recent_order_by_ip($customer_ip);

        if ($recent_order_by_phone || $recent_order_by_ip) {
            $error_message = __('لا يمكنك تقديم طلب آخر خلال 24 ساعة. يرجى المحاولة مرة أخرى غداً.', 'rid-cod');
            
            // Log the blocked attempt for debugging
            error_log(sprintf(
                'RID COD: Blocked duplicate order attempt - Phone: %s, IP: %s, Phone match: %s, IP match: %s',
                $phone,
                $customer_ip,
                $recent_order_by_phone ? 'Yes' : 'No',
                $recent_order_by_ip ? 'Yes' : 'No'
            ));
            
            throw new Exception($error_message);
        }
    }

    /**
     * Record order information for future limit checking
     * 
     * @param int $order_id The created order ID
     * @param array $order_data The order data
     */
    public function record_order($order_id, $order_data) {
        // Check if the feature is enabled
        $prevent_24h_orders = get_option('rid_cod_prevent_2nd_order_24h', 'no') === 'yes';
        
        if (!$prevent_24h_orders) {
            return; // Feature disabled, don't record
        }

        $phone = isset($order_data['phone']) ? sanitize_text_field($order_data['phone']) : '';
        $customer_ip = $this->get_customer_ip();

        // Clean phone number
        $phone = preg_replace('/\s+/', '', $phone);

        if (!empty($phone)) {
            // Record phone number with timestamp
            $this->record_phone_order($phone, $order_id);
        }

        if (!empty($customer_ip)) {
            // Record IP address with timestamp
            $this->record_ip_order($customer_ip, $order_id);
        }

        // Log successful recording
        error_log(sprintf(
            'RID COD: Recorded order for limit checking - Order ID: %d, Phone: %s, IP: %s',
            $order_id,
            $phone,
            $customer_ip
        ));
    }

    /**
     * Check if there's a recent order by phone number within 24 hours
     * 
     * @param string $phone The phone number to check
     * @return bool True if recent order exists
     */
    private function has_recent_order_by_phone($phone) {
        if (empty($phone)) {
            return false;
        }

        // Get stored phone orders
        $phone_orders = get_option('rid_cod_phone_orders', array());
        
        // Clean expired entries and check for recent orders
        $current_time = time();
        $twenty_four_hours = 24 * 60 * 60; // 24 hours in seconds
        $found_recent = false;

        foreach ($phone_orders as $stored_phone => $order_data) {
            // Remove expired entries (older than 24 hours)
            if (($current_time - $order_data['timestamp']) > $twenty_four_hours) {
                unset($phone_orders[$stored_phone]);
                continue;
            }

            // Check if this phone number has a recent order
            if ($stored_phone === $phone) {
                $found_recent = true;
            }
        }

        // Update the cleaned phone orders list
        update_option('rid_cod_phone_orders', $phone_orders);

        return $found_recent;
    }

    /**
     * Check if there's a recent order by IP address within 24 hours
     * 
     * @param string $ip The IP address to check
     * @return bool True if recent order exists
     */
    private function has_recent_order_by_ip($ip) {
        if (empty($ip)) {
            return false;
        }

        // Get stored IP orders
        $ip_orders = get_option('rid_cod_ip_orders', array());
        
        // Clean expired entries and check for recent orders
        $current_time = time();
        $twenty_four_hours = 24 * 60 * 60; // 24 hours in seconds
        $found_recent = false;

        foreach ($ip_orders as $stored_ip => $order_data) {
            // Remove expired entries (older than 24 hours)
            if (($current_time - $order_data['timestamp']) > $twenty_four_hours) {
                unset($ip_orders[$stored_ip]);
                continue;
            }

            // Check if this IP address has a recent order
            if ($stored_ip === $ip) {
                $found_recent = true;
            }
        }

        // Update the cleaned IP orders list
        update_option('rid_cod_ip_orders', $ip_orders);

        return $found_recent;
    }

    /**
     * Record a phone number order
     * 
     * @param string $phone The phone number
     * @param int $order_id The order ID
     */
    private function record_phone_order($phone, $order_id) {
        $phone_orders = get_option('rid_cod_phone_orders', array());
        
        $phone_orders[$phone] = array(
            'order_id' => $order_id,
            'timestamp' => time()
        );

        update_option('rid_cod_phone_orders', $phone_orders);
    }

    /**
     * Record an IP address order
     * 
     * @param string $ip The IP address
     * @param int $order_id The order ID
     */
    private function record_ip_order($ip, $order_id) {
        $ip_orders = get_option('rid_cod_ip_orders', array());
        
        $ip_orders[$ip] = array(
            'order_id' => $order_id,
            'timestamp' => time()
        );

        update_option('rid_cod_ip_orders', $ip_orders);
    }

    /**
     * Get customer IP address
     * 
     * @return string The customer's IP address
     */
    private function get_customer_ip() {
        // Check for various IP headers in order of preference
        $ip_headers = array(
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        );

        foreach ($ip_headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                
                // Handle comma-separated IPs (X-Forwarded-For can contain multiple IPs)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP address
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        // Fallback to REMOTE_ADDR even if it's a private IP
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '';
    }

    /**
     * Clean up expired order records (called by cron job)
     * This method can be called periodically to clean up old records
     */
    public static function cleanup_expired_records() {
        $current_time = time();
        $twenty_four_hours = 24 * 60 * 60;

        // Clean phone orders
        $phone_orders = get_option('rid_cod_phone_orders', array());
        foreach ($phone_orders as $phone => $order_data) {
            if (($current_time - $order_data['timestamp']) > $twenty_four_hours) {
                unset($phone_orders[$phone]);
            }
        }
        update_option('rid_cod_phone_orders', $phone_orders);

        // Clean IP orders
        $ip_orders = get_option('rid_cod_ip_orders', array());
        foreach ($ip_orders as $ip => $order_data) {
            if (($current_time - $order_data['timestamp']) > $twenty_four_hours) {
                unset($ip_orders[$ip]);
            }
        }
        update_option('rid_cod_ip_orders', $ip_orders);

        error_log('RID COD: Cleaned up expired order limit records');
    }
}

// Initialize the order limiter
new RID_COD_Order_Limiter();
