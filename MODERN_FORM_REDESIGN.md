# النموذج الثاني - التصميم الجديد

## نظرة عامة
تم إعادة تصميم "النموذج العصري" ليصبح "النموذج الثاني" بناءً على التصميم المطلوب في الصورة.

## التغييرات المنجزة

### 1. تغيير الاسم
- تم تغيير اسم "النموذج العصري" إلى "النموذج الثاني"
- التحديث في واجهة الإعدادات وفي معاينة الأنماط

### 2. التصميم العام
- **الخلفية**: لون بنفسجي فاتح (#f8f6ff) مع إطار بنفسجي (#b794f6)
- **الشكل**: مستطيل مدور بزوايا ناعمة (15px)
- **الظل**: ظل خفيف بنفسجي للعمق
- **العرض الأقصى**: 600px مع توسيط تلقائي

### 3. العنوان
- لون النص: رمادي داكن (#333)
- الحجم: 18px
- الوزن: 600 (متوسط السماكة)
- المحاذاة: وسط

### 4. الحقول
- **التخطيط**: حقلين في صف واحد (الاسم والهاتف، المدينة والمنطقة)
- **التصميم**: خلفية بيضاء مع إطار رمادي فاتح
- **التفاعل**: إطار بنفسجي عند التركيز مع ظل خفيف
- **الأبعاد**: ارتفاع ثابت 48px مع padding مناسب

### 5. المتغيرات (الألوان والأحجام)

#### الألوان:
- **التصميم**: دوائر ملونة بدلاً من القائمة المنسدلة
- **الألوان المدعومة**: أحمر (#e53e3e)، أخضر (#38a169)، أسود (#2d3748)
- **التفاعل**: تكبير عند التحديد مع علامة ✓ بيضاء
- **الحدود**: رمادية فاتحة تتحول لسوداء عند التحديد

#### الأحجام:
- **التصميم**: مربعات مدورة مع النص داخلها
- **الأحجام**: XS, S, M, L, XL
- **التفاعل**: خلفية سوداء مع نص أبيض عند التحديد
- **التخطيط**: صف أفقي مع مسافات مناسبة

### 6. الزر وعداد الكمية
- **الزر**: لون بنفسجي (#b794f6) مع زوايا مدورة 8px
- **التفاعل**: يصبح أغمق عند التمرير مع حركة خفيفة للأعلى
- **عداد الكمية**: تصميم مسطح مع أزرار رمادية فاتحة
- **التخطيط**: الزر يأخذ المساحة المتبقية، العداد بعرض ثابت

### 7. ملخص الطلب
- **الخلفية**: أبيض مع إطار رمادي فاتح
- **الرأس**: خلفية رمادية فاتحة (#f7fafc) مع نص متوسط
- **المحتوى**: جدول منظم مع خطوط فاصلة خفيفة
- **الإجمالي**: خلفية مميزة مع خط عريض

### 8. الاستجابة للأجهزة المحمولة
- **الحقول**: تتحول لعمود واحد على الشاشات الصغيرة
- **المتغيرات**: تتوسط في الشاشات الصغيرة
- **الأزرار**: تأخذ العرض الكامل على الموبايل

## الملفات المعدلة

### 1. `includes/class-rid-cod-customizer.php`
- تغيير اسم النموذج من "العصري" إلى "الثاني"
- تحديث النص في معاينة الأنماط

### 2. `assets/css/rid-cod.css`
- إعادة كتابة كامل CSS للنموذج العصري (.rid-cod-form-modern)
- إضافة أنماط للمتغيرات التفاعلية
- تحسين الاستجابة للأجهزة المحمولة

### 3. `assets/js/rid-cod.js`
- إضافة دالة `initModernFormVariations()` للتفاعل
- تحويل قوائم الألوان والأحجام لعناصر تفاعلية
- ربط التفاعل مع WooCommerce variations

## المميزات الجديدة

### 1. تفاعل الألوان
- النقر على اللون يحدده تلقائياً
- علامة ✓ تظهر على اللون المحدد
- تأثيرات بصرية عند التمرير والتحديد

### 2. تفاعل الأحجام
- النقر على الحجم يحدده تلقائياً
- تغيير لون الخلفية والنص عند التحديد
- تأثيرات بصرية عند التمرير

### 3. تصميم متجاوب
- يتكيف مع جميع أحجام الشاشات
- تخطيط مرن للحقول والعناصر
- تحسينات خاصة للأجهزة المحمولة

## التوافق
- متوافق مع جميع متصفحات الويب الحديثة
- يعمل مع WooCommerce variations
- متوافق مع إعدادات الإضافة الحالية
- لا يؤثر على النماذج الأخرى (الكلاسيكي والأحمر)

## الاستخدام
1. اذهب إلى **WooCommerce > إعدادات RID COD**
2. انتقل إلى تبويبة **"إعدادات المظهر"**
3. اختر **"النموذج الثاني"** من خيارات شكل النموذج
4. احفظ الإعدادات

سيظهر النموذج بالتصميم الجديد تماماً كما في الصورة المطلوبة! 🎨
